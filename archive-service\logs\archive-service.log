{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:44:32:4432"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Failed to connect to database, but continuing in development mode","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Make sure to run the init-databases.sql script to set up the database","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 16:45:35:4535"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 16:45:35:4535","version":"1.0.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:49:39:4939"}
{"database":"disconnected","level":"info","message":"Health check performed","status":"unhealthy","timestamp":"2025-07-15 16:49:39:4939"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 18:23:43:2343"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 18:23:43:2343"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 18:23:43:2343"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 18:23:43:2343"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 18:23:43:2343","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 18:23:59:2359"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 18:23:59:2359"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 18:49:06:496"}
{"level":"warn","message":"Failed to connect to database, but continuing in development mode","timestamp":"2025-07-15 18:49:06:496"}
{"level":"warn","message":"Make sure to run the init-databases.sql script to set up the database","timestamp":"2025-07-15 18:49:06:496"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 18:49:06:496"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 18:49:06:496","version":"1.0.0"}
{"level":"info","message":"Received SIGINT, starting graceful shutdown","timestamp":"2025-07-15 18:49:15:4915"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 19:02:14:214"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:02:14:214"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 19:02:14:214"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 19:02:14:214"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 19:02:14:214","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 19:04:12:412"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-15 19:04:12:412"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-15 19:04:12:412"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 19:04:12:412"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 19:04:12:412","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 03:57:46:5746"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 03:57:46:5746"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 03:57:46:5746"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 03:57:46:5746"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 03:57:46:5746","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 21:48:59:4859"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 21:48:59:4859"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 21:48:59:4859","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:49:03:493"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:49:03:493"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:49:33:4933"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:49:33:4933"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:03:503"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:50:03:503"}
{"database":"atma_db","error":"connect ECONNREFUSED **********:5432","host":"postgres","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 21:50:36:5036"}
{"database":"disconnected","level":"info","message":"Health check performed","status":"unhealthy","timestamp":"2025-07-15 21:50:36:5036"}
{"level":"info","message":"Received SIGTERM, starting graceful shutdown","timestamp":"2025-07-15 21:50:52:5052"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:53:5053"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 21:50:54:5054"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 21:50:54:5054"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 21:50:54:5054"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 21:50:54:5054","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:50:58:5058"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:50:58:5058"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:51:28:5128"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:51:28:5128"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:51:58:5158"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:51:58:5158"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:52:28:5228"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:52:28:5228"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:52:58:5258"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:52:58:5258"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:53:28:5328"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:53:28:5328"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:53:58:5358"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:53:58:5358"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:01:541"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:01:541"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:28:5428"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:28:5428"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:54:58:5458"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:54:58:5458"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:28:5528"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:28:5528"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:55:5555"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:55:5555"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:55:58:5558"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:55:58:5558"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:56:29:5629"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:56:29:5629"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:56:59:5659"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:56:59:5659"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:19:5719"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:19:5719"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:29:5729"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:29:5729"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:57:59:5759"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:57:59:5759"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:58:29:5829"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:58:29:5829"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:58:59:5859"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:58:59:5859"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:59:29:5929"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:59:29:5929"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 21:59:59:5959"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 21:59:59:5959"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:00:29:029"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:00:29:029"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:00:59:059"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:00:59:059"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:01:29:129"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:01:29:129"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:01:59:159"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:01:59:159"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:02:29:229"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:02:29:229"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:02:59:259"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:02:59:259"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:03:30:330"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:03:30:330"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:04:00:40"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:04:00:40"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:04:30:430"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:04:30:430"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:05:00:50"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:05:00:50"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:05:30:530"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:05:30:530"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:06:00:60"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:06:00:60"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:06:30:630"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:06:30:630"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:07:00:70"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:07:00:70"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:07:30:730"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:07:30:730"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:08:00:80"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:08:00:80"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:08:30:830"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:08:30:830"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:09:00:90"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:09:00:90"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:09:30:930"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:09:30:930"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:10:01:101"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:10:01:101"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:10:31:1031"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:10:31:1031"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:11:01:111"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:11:01:111"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:11:31:1131"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:11:31:1131"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:12:01:121"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:12:01:121"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:12:31:1231"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:12:31:1231"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:13:01:131"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:13:01:131"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:13:31:1331"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:13:31:1331"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:14:01:141"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:14:01:141"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:14:31:1431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:14:31:1431"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:15:01:151"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:15:01:151"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:15:31:1531"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:15:31:1531"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:16:01:161"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:16:01:161"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:16:31:1631"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:16:31:1631"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:17:02:172"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:17:02:172"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:17:32:1732"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:17:32:1732"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:18:02:182"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:18:02:182"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:18:32:1832"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:18:32:1832"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:19:02:192"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:19:02:192"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:19:32:1932"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:19:32:1932"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:20:02:202"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:20:02:202"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:20:32:2032"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:20:32:2032"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:21:02:212"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:21:02:212"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:21:32:2132"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:21:32:2132"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:22:02:222"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:22:02:222"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:22:32:2232"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:22:32:2232"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:23:02:232"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:23:02:232"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:23:33:2333"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:23:33:2333"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:24:03:243"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:24:03:243"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:24:33:2433"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:24:33:2433"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:25:03:253"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:25:03:253"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:25:33:2533"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:25:33:2533"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:26:03:263"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:26:03:263"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:26:33:2633"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:26:33:2633"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:27:03:273"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:27:03:273"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:27:33:2733"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:27:33:2733"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:28:03:283"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:28:03:283"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:28:33:2833"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:28:33:2833"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:29:03:293"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:29:03:293"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:29:34:2934"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:29:34:2934"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:30:04:304"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:30:04:304"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:30:34:3034"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:30:34:3034"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:04:314"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:04:314"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:34:3134"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:34:3134"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:31:59:3159"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:31:59:3159"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:32:04:324"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:32:04:324"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:32:34:3234"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:32:34:3234"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:33:04:334"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:33:04:334"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:33:34:3334"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:33:34:3334"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:34:04:344"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:34:04:344"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:34:34:3434"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:34:34:3434"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:35:04:354"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:35:04:354"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:35:35:3535"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:35:35:3535"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:36:05:365"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:36:05:365"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:36:35:3635"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:36:35:3635"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:37:05:375"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:37:05:375"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:37:35:3735"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:37:35:3735"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:38:05:385"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:38:05:385"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:38:35:3835"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:38:35:3835"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:39:05:395"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:39:05:395"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:39:35:3935"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:39:35:3935"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:40:05:405"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:40:05:405"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:40:35:4035"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:40:35:4035"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:41:05:415"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:41:05:415"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:41:36:4136"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:41:36:4136"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:42:06:426"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:42:06:426"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:42:36:4236"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:42:36:4236"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:43:06:436"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:43:06:436"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:43:36:4336"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:43:36:4336"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:44:06:446"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:44:06:446"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:44:36:4436"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:44:36:4436"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:45:06:456"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:45:06:456"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:45:36:4536"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:45:36:4536"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:46:06:466"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:46:06:466"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:46:36:4636"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:46:36:4636"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:47:07:477"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:47:07:477"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:47:37:4737"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:47:37:4737"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:48:07:487"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:48:07:487"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:48:37:4837"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:48:37:4837"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:49:07:497"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:49:07:497"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:49:37:4937"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:49:37:4937"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:50:07:507"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:50:07:507"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:50:37:5037"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:50:37:5037"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:51:07:517"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:51:07:517"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:51:37:5137"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:51:37:5137"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:52:07:527"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:52:07:527"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:52:38:5238"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:52:38:5238"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:53:08:538"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:53:08:538"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:53:38:5338"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:53:38:5338"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:54:08:548"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:54:08:548"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:54:38:5438"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:54:38:5438"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:55:08:558"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:55:08:558"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:55:38:5538"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:55:38:5538"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:56:08:568"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:56:08:568"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:56:38:5638"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:56:38:5638"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:57:08:578"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:57:08:578"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:57:39:5739"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:57:39:5739"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:58:09:589"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:58:09:589"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:58:39:5839"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:58:39:5839"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:59:09:599"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:59:09:599"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 22:59:39:5939"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 22:59:39:5939"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:00:09:09"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:00:09:09"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:00:39:039"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:00:39:039"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:01:09:19"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:01:09:19"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:01:39:139"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:01:39:139"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:02:09:29"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:02:09:29"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:02:39:239"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:02:39:239"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:03:09:39"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:03:09:39"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:03:39:339"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:03:39:339"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:04:10:410"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:04:10:410"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:04:40:440"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:04:40:440"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:05:10:510"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:05:10:510"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:05:40:540"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:05:40:540"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:06:10:610"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:06:10:610"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:06:40:640"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:06:40:640"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:07:10:710"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:07:10:710"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:07:40:740"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:07:40:740"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:08:10:810"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:08:10:810"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:08:40:840"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:08:40:840"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:09:10:910"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:09:10:910"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:09:41:941"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:09:41:941"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:10:11:1011"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:10:11:1011"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:10:41:1041"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:10:41:1041"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:11:11:1111"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:11:11:1111"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:11:41:1141"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:11:41:1141"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:12:11:1211"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:12:11:1211"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:12:41:1241"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:12:41:1241"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:13:11:1311"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:13:11:1311"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:13:41:1341"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:13:41:1341"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:14:11:1411"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:14:11:1411"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:14:41:1441"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:14:41:1441"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:15:11:1511"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:15:11:1511"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:15:42:1542"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:15:42:1542"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:16:12:1612"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:16:12:1612"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:16:42:1642"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:16:42:1642"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:17:12:1712"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:17:12:1712"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:17:42:1742"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:17:42:1742"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:18:13:1813"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:18:13:1813"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:18:43:1843"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:18:43:1843"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:19:13:1913"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:19:13:1913"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:19:43:1943"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:19:43:1943"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:20:14:2014"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:20:14:2014"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:20:44:2044"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:20:44:2044"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:21:14:2114"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:21:14:2114"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:21:44:2144"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:21:44:2144"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:22:14:2214"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:22:14:2214"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:22:44:2244"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:22:44:2244"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:23:14:2314"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:23:14:2314"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:23:44:2344"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:23:44:2344"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:24:14:2414"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:24:14:2414"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:24:45:2445"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:24:45:2445"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:25:15:2515"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:25:15:2515"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:25:45:2545"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:25:45:2545"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:26:15:2615"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:26:15:2615"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:26:45:2645"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:26:45:2645"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:27:15:2715"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:27:15:2715"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:27:45:2745"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:27:45:2745"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:28:15:2815"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:28:15:2815"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:28:45:2845"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:28:45:2845"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:29:16:2916"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:29:16:2916"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:29:46:2946"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:29:46:2946"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:30:16:3016"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:30:16:3016"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:30:46:3046"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:30:46:3046"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:31:16:3116"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:31:16:3116"}
{"level":"info","message":"Received SIGTERM, starting graceful shutdown","timestamp":"2025-07-15 23:31:42:3142"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"Database models synchronized","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-15 23:32:38:3238"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 23:32:38:3238"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 23:32:38:3238","version":"1.0.0"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:32:42:3242"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:32:42:3242"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:33:12:3312"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:33:12:3312"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:33:42:3342"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:33:42:3342"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:34:12:3412"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:34:12:3412"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:34:42:3442"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:34:42:3442"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:35:12:3512"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:35:12:3512"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:35:42:3542"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:35:42:3542"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:36:12:3612"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:36:12:3612"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:36:43:3643"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:36:43:3643"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:37:13:3713"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:37:13:3713"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:37:43:3743"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:37:43:3743"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:38:13:3813"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:38:13:3813"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:38:43:3843"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:38:43:3843"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:39:13:3913"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:39:13:3913"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:39:43:3943"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:39:43:3943"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:40:13:4013"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:40:13:4013"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:40:43:4043"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:40:43:4043"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:41:13:4113"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:41:13:4113"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:41:43:4143"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:41:43:4143"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-15 23:42:13:4213"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-15 23:42:13:4213"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 08:21:26:2126"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 08:21:26:2126"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 08:21:26:2126"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 08:21:26:2126"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 08:21:26:2126","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:16:18:1618"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:16:18:1618"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:16:18:1618"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:16:18:1618"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:16:18:1618","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:18:03:183"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:18:03:183"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:18:03:183"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:18:03:183"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:18:03:183","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:20:59:2059"}
{"error":"must be owner of table analysis_results","level":"error","message":"Database initialization failed","timestamp":"2025-07-16 13:20:59:2059"}
{"level":"warn","message":"Continuing in development mode despite database error","timestamp":"2025-07-16 13:20:59:2059"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:20:59:2059"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:20:59:2059","version":"1.0.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 13:26:05:265"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 13:26:05:265"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 13:26:05:265"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 13:26:05:265","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:26:33:2633","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:26:33:2633","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:26:33:2633","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:26:33:2633","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:26:33:2633","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:26:33:2633","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:28:54:2854","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:28:54:2854","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 13:28:54:2854","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:28:54:2854","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 13:28:54:2854","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 13:28:54:2854","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:41:41:4141"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 14:41:41:4141"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 14:41:41:4141"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 14:41:41:4141","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:42:04:424","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:42:04:424","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:42:04:424","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:42:04:424","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:42:04:424","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:42:04:424","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:00:430","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:00:430","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:00:430","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:00:430","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:00:430","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:00:430","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:01:431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:01:431"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:01:431"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:01:431"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:43:08:438"}
{"database":"connected","level":"info","message":"Health check performed","status":"healthy","timestamp":"2025-07-16 14:43:08:438"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:11:4311","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:11:4311","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:11:4311","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:43:11:4311","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:43:11:4311","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:43:11:4311","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","port":"5432","schema":"archive","timestamp":"2025-07-16 14:46:14:4614"}
{"level":"info","message":"Database initialized successfully","timestamp":"2025-07-16 14:46:14:4614"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-16 14:46:14:4614"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-16 14:46:14:4614","version":"1.0.0"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:44:4644","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:44:4644","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:44:4644","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:44:4644","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:44:4644","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:44:4644","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:57:4657","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:57:4657","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"warn","message":"Unauthorized access to internal endpoint","method":"GET","path":"/stats/summary","timestamp":"2025-07-16 14:46:57:4657","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:57:4657","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Fetching analysis results for user","options":{"limit":10,"order":"desc","page":1,"sort":"created_at"},"timestamp":"2025-07-16 14:46:57:4657","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"count":0,"level":"info","message":"Analysis results fetched successfully","timestamp":"2025-07-16 14:46:57:4657","total":0,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
