/**
 * Statistics Controller
 * Handles HTTP requests for statistics and analytics
 */

const statsService = require('../services/statsService');
const logger = require('../utils/logger');

/**
 * Get user statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getUserStats = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const stats = await statsService.getUserStats(userId);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user overview statistics (frontend accessible)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getUserOverview = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get user-specific overview data (safe for frontend)
    const overview = await statsService.getUserOverview(userId);

    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get summary statistics (internal service only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSummaryStats = async (req, res, next) => {
  try {
    const stats = await statsService.getSummaryStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getUserStats,
  getUserOverview,
  getSummaryStats
};
