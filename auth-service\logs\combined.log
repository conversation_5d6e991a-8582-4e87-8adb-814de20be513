{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:27"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"errors":["Email must be a valid email address","Password must be at least 8 characters long","Password must contain at least one letter and one number"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"jwt malformed","level":"warn","message":"JWT token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27","tokenType":"JsonWebTokenError"}
{"error":"Invalid token format","ip":"::1","level":"warn","message":"Token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 18:48:02"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:02:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:02:16"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:57:28"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:02","url":"/auth/token-balance"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:51","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:14:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:14:45","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:15:56"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":4,"oldBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","tokenBalance":4,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":3,"oldBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","tokenBalance":3,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:31:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:34:09"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"192d655f-d6fa-4cb1-a07f-8cf98cb6ec5b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 200 117 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:52:06","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 201 184 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Email already exists","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe9ffc0e-5be5-430a-ae54-b439a18f2e55","service":"auth-service","stack":"Error: Email already exists\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:97:50)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 82 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 200 172 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Invalid email or password","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"5c6d045a-d3a4-4d2e-9715-efa3d18df1e2","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:177:47)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Cannot read properties of undefined (reading 'id')","ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["\"userId\" must be a valid GUID","Amount is required","Operation is required"],"level":"warn","message":"Request validation failed","method":"PUT","path":"/token-balance","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 400 174 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Internal service authentication failed: Missing internal service header","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/token-balance"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-16 08:17:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","tokenBalance":3,"userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
